//! # Fred 10.1 缓存兼容性清理工具
//!
//! 命令行工具，用于清理 fred 版本升级导致的不兼容缓存数据
//!
//! ## 使用方法
//!
//! ```bash
//! # 使用默认配置清理缓存
//! cargo run --bin cache_cleanup
//!
//! # 使用自定义配置清理缓存
//! DRAGONFLY_URL=redis://localhost:6379 cargo run --bin cache_cleanup
//! ```

use anyhow::{Result as AnyhowResult, anyhow};
use app_infrastructure::cache::{CacheConfig, run_cleanup_command};
use std::env;
use tracing::{error, info};

#[tokio::main]
async fn main() -> AnyhowResult<()> {
    // 初始化日志系统
    init_logging()?;

    info!("🚀 启动 Fred 10.1 缓存兼容性清理工具");

    // 从环境变量或使用默认配置创建缓存配置
    let config = create_cache_config()?;

    info!("📋 缓存配置:");
    info!("   URL: {}", config.cache_url);
    info!("   键前缀: {}", config.key_prefix);
    info!("   连接池大小: {}", config.pool_config.max_connections);
    info!("   连接超时: {:?}", config.pool_config.connect_timeout);

    // 执行清理命令
    match run_cleanup_command(config).await {
        Ok(_) => {
            info!("✅ 缓存清理工具执行成功");
            Ok(())
        }
        Err(e) => {
            error!("❌ 缓存清理工具执行失败: {}", e);
            Err(e)
        }
    }
}

/// 初始化日志系统
fn init_logging() -> AnyhowResult<()> {
    // 简化的日志初始化，避免依赖 tracing_subscriber
    println!("缓存清理工具日志系统已初始化");
    Ok(())
}

/// 从环境变量创建缓存配置
fn create_cache_config() -> AnyhowResult<CacheConfig> {
    // 从环境变量获取配置，或使用默认值
    let url = env::var("DRAGONFLY_URL")
        .or_else(|_| env::var("REDIS_URL"))
        .unwrap_or_else(|_| "redis://localhost:6379".to_string());

    let key_prefix = env::var("CACHE_KEY_PREFIX").unwrap_or_else(|_| "axum_chat".to_string());

    let max_pool_size = env::var("CACHE_POOL_SIZE")
        .unwrap_or_else(|_| "10".to_string())
        .parse::<u32>()
        .map_err(|e| anyhow!("无效的连接池大小: {}", e))?;

    let connection_timeout_secs = env::var("CACHE_CONNECTION_TIMEOUT")
        .unwrap_or_else(|_| "30".to_string())
        .parse::<u64>()
        .map_err(|e| anyhow!("无效的连接超时时间: {}", e))?;

    let config = CacheConfig {
        cache_url: url,
        key_prefix,
        pool_config: app_infrastructure::cache::CachePoolConfig {
            max_connections: max_pool_size,
            min_connections: 2,
            connect_timeout: std::time::Duration::from_secs(connection_timeout_secs),
            acquire_timeout: std::time::Duration::from_secs(30),
            idle_timeout: std::time::Duration::from_secs(300),
            tcp_nodelay: true,
            tcp_keepalive: false,
            max_lifetime: Some(std::time::Duration::from_secs(1800)),
            max_reconnect_attempts: 5,
            reconnect_delay: std::time::Duration::from_secs(1),
        },
        default_ttl: 3600,
        enable_compression: false,
        cluster_mode: false,
        cluster_nodes: vec![],
    };

    Ok(config)
}

/// 打印帮助信息
fn print_help() {
    println!("Fred 10.1 缓存兼容性清理工具");
    println!("==============================");
    println!();
    println!("用途: 清理 fred 版本升级（6.0 -> 10.1）导致的不兼容缓存数据");
    println!();
    println!("使用方法:");
    println!("  cargo run --bin cache_cleanup");
    println!();
    println!("环境变量:");
    println!("  DRAGONFLY_URL          - DragonflyDB/Redis 连接URL (默认: redis://localhost:6379)");
    println!("  REDIS_URL              - Redis 连接URL (DRAGONFLY_URL 的别名)");
    println!("  CACHE_KEY_PREFIX       - 缓存键前缀 (默认: axum_chat)");
    println!("  CACHE_POOL_SIZE        - 连接池大小 (默认: 10)");
    println!("  CACHE_CONNECTION_TIMEOUT - 连接超时时间(秒) (默认: 30)");
    println!("  RUST_LOG               - 日志级别 (默认: info)");
    println!();
    println!("示例:");
    println!("  # 使用默认配置");
    println!("  cargo run --bin cache_cleanup");
    println!();
    println!("  # 使用自定义 DragonflyDB URL");
    println!("  DRAGONFLY_URL=redis://localhost:6379 cargo run --bin cache_cleanup");
    println!();
    println!("  # 启用调试日志");
    println!("  RUST_LOG=debug cargo run --bin cache_cleanup");
    println!();
    println!("功能:");
    println!("  1. 健康检查 - 验证缓存服务连接状态");
    println!("  2. 统计信息 - 显示当前缓存连接池状态");
    println!("  3. 功能测试 - 测试缓存读写功能");
    println!("  4. 数据清理 - 扫描并清理不兼容的缓存数据");
    println!();
    println!("注意事项:");
    println!("  - 此工具会自动检测并清理不兼容的缓存数据");
    println!("  - 清理过程是安全的，不会影响兼容的缓存数据");
    println!("  - 建议在升级 fred 版本后运行此工具");
    println!("  - 清理过程中系统仍可正常运行（会从数据库重新获取数据）");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_cache_config_with_defaults() {
        // 清除环境变量
        env::remove_var("DRAGONFLY_URL");
        env::remove_var("REDIS_URL");
        env::remove_var("CACHE_KEY_PREFIX");
        env::remove_var("CACHE_POOL_SIZE");
        env::remove_var("CACHE_CONNECTION_TIMEOUT");

        let config = create_cache_config().unwrap();

        assert_eq!(config.url, "redis://localhost:6379");
        assert_eq!(config.key_prefix, "axum_chat");
        assert_eq!(config.pool.max_size, 10);
        assert_eq!(
            config.pool.connection_timeout,
            std::time::Duration::from_secs(30)
        );
    }

    #[test]
    fn test_create_cache_config_with_env_vars() {
        env::set_var("DRAGONFLY_URL", "redis://test:6379");
        env::set_var("CACHE_KEY_PREFIX", "test_prefix");
        env::set_var("CACHE_POOL_SIZE", "20");
        env::set_var("CACHE_CONNECTION_TIMEOUT", "60");

        let config = create_cache_config().unwrap();

        assert_eq!(config.url, "redis://test:6379");
        assert_eq!(config.key_prefix, "test_prefix");
        assert_eq!(config.pool.max_size, 20);
        assert_eq!(
            config.pool.connection_timeout,
            std::time::Duration::from_secs(60)
        );

        // 清理环境变量
        env::remove_var("DRAGONFLY_URL");
        env::remove_var("CACHE_KEY_PREFIX");
        env::remove_var("CACHE_POOL_SIZE");
        env::remove_var("CACHE_CONNECTION_TIMEOUT");
    }

    #[test]
    fn test_create_cache_config_with_redis_url_fallback() {
        env::remove_var("DRAGONFLY_URL");
        env::set_var("REDIS_URL", "redis://fallback:6379");

        let config = create_cache_config().unwrap();

        assert_eq!(config.url, "redis://fallback:6379");

        // 清理环境变量
        env::remove_var("REDIS_URL");
    }

    #[test]
    fn test_create_cache_config_invalid_pool_size() {
        env::set_var("CACHE_POOL_SIZE", "invalid");

        let result = create_cache_config();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("无效的连接池大小"));

        // 清理环境变量
        env::remove_var("CACHE_POOL_SIZE");
    }

    #[test]
    fn test_create_cache_config_invalid_timeout() {
        env::set_var("CACHE_CONNECTION_TIMEOUT", "invalid");

        let result = create_cache_config();
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("无效的连接超时时间")
        );

        // 清理环境变量
        env::remove_var("CACHE_CONNECTION_TIMEOUT");
    }
}
