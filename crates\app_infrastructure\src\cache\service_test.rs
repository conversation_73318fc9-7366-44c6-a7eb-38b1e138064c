//! # 缓存服务测试
//!
//! 测试多层缓存策略的实现，包括热数据、温数据、冷数据的TTL管理
//! 基于fred库的DragonflyDB/Redis缓存功能全面测试

use super::client_manager::CacheClientManager;
use super::config::CacheConfig;
use super::service::*;
use anyhow::Result as AnyhowResult;
use serde::{ Deserialize, Serialize };
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use tracing::info;

/// 测试用的数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
struct TestData {
    id: u32,
    name: String,
    value: f64,
}

impl TestData {
    fn new(id: u32, name: &str, value: f64) -> Self {
        Self {
            id,
            name: name.to_string(),
            value,
        }
    }
}

/// 创建测试用的缓存服务
async fn create_test_cache_service() -> AnyhowResult<Arc<DragonflyCache>> {
    // 跳过需要Redis服务器的测试
    if std::env::var("SKIP_REDIS_TESTS").is_ok() {
        return Err(anyhow::anyhow!("跳过Redis测试"));
    }

    let config = CacheConfig::for_tests();
    let manager = Arc::new(CacheClientManager::new(config).await?);
    let service = Arc::new(DragonflyCache::new(manager));
    Ok(service)
}

#[tokio::test]
async fn test_cache_service_basic_operations() {
    let service = match create_test_cache_service().await {
        Ok(s) => s,
        Err(_) => {
            println!("跳过Redis测试 - 服务器不可用");
            return;
        }
    };

    let test_data = TestData::new(1, "测试数据", 42.0);
    let key = "test:basic:1";

    // 测试设置缓存
    assert!(service.set(key, &test_data, Some(Duration::from_secs(60))).await.is_ok());

    // 测试获取缓存
    let retrieved: Option<TestData> = service.get(key).await.unwrap();
    assert!(retrieved.is_some());
    assert_eq!(retrieved.unwrap(), test_data);

    // 测试键存在性检查
    assert!(service.exists(key).await.unwrap());

    // 测试删除缓存
    assert!(service.delete(key).await.unwrap());
    assert!(!service.exists(key).await.unwrap());
}

#[tokio::test]
async fn test_cache_service_ttl_operations() {
    let service = match create_test_cache_service().await {
        Ok(s) => s,
        Err(_) => {
            println!("跳过Redis测试 - 服务器不可用");
            return;
        }
    };

    let test_data = TestData::new(2, "TTL测试", 100.0);
    let key = "test:ttl:2";

    // 设置短TTL的缓存
    assert!(service.set(key, &test_data, Some(Duration::from_secs(2))).await.is_ok());

    // 检查TTL
    let ttl = service.ttl(key).await.unwrap();
    assert!(ttl > 0 && ttl <= 2);

    // 等待过期
    sleep(Duration::from_secs(3)).await;

    // 验证已过期
    let retrieved: Option<TestData> = service.get(key).await.unwrap();
    assert!(retrieved.is_none());
}

#[tokio::test]
async fn test_cache_service_batch_operations() {
    let service = match create_test_cache_service().await {
        Ok(s) => s,
        Err(_) => {
            println!("跳过Redis测试 - 服务器不可用");
            return;
        }
    };

    let test_data1 = TestData::new(3, "批量测试1", 200.0);
    let test_data2 = TestData::new(4, "批量测试2", 300.0);
    let test_data3 = TestData::new(5, "批量测试3", 400.0);

    let pairs = vec![
        ("test:batch:3", &test_data1),
        ("test:batch:4", &test_data2),
        ("test:batch:5", &test_data3)
    ];

    // 批量设置
    assert!(service.mset(&pairs, Some(Duration::from_secs(60))).await.is_ok());

    // 批量获取
    let keys = vec!["test:batch:3", "test:batch:4", "test:batch:5"];
    let results: Vec<Option<TestData>> = service.mget(&keys).await.unwrap();

    assert_eq!(results.len(), 3);
    assert_eq!(results[0].as_ref().unwrap(), &test_data1);
    assert_eq!(results[1].as_ref().unwrap(), &test_data2);
    assert_eq!(results[2].as_ref().unwrap(), &test_data3);

    // 清理
    for key in &keys {
        let _ = service.delete(key).await;
    }
}

/// 多层缓存策略测试
mod multi_tier_cache_tests {
    use super::*;

    #[tokio::test]
    async fn test_hot_data_cache_strategy() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let hot_data = TestData::new(100, "热数据", 1000.0);
        let key = "hot:user:session:100";

        // 热数据使用短TTL (5分钟)
        let hot_ttl = Duration::from_secs(5 * 60);
        assert!(service.set(key, &hot_data, Some(hot_ttl)).await.is_ok());

        // 验证TTL设置正确
        let ttl = service.ttl(key).await.unwrap();
        assert!(ttl > 290 && ttl <= 300); // 允许一些时间误差

        // 清理
        let _ = service.delete(key).await;
    }

    #[tokio::test]
    async fn test_warm_data_cache_strategy() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let warm_data = TestData::new(200, "温数据", 2000.0);
        let key = "warm:room:members:200";

        // 温数据使用中等TTL (30分钟)
        let warm_ttl = Duration::from_secs(30 * 60);
        assert!(service.set(key, &warm_data, Some(warm_ttl)).await.is_ok());

        // 验证TTL设置正确
        let ttl = service.ttl(key).await.unwrap();
        assert!(ttl > 1790 && ttl <= 1800); // 允许一些时间误差

        // 清理
        let _ = service.delete(key).await;
    }

    #[tokio::test]
    async fn test_cold_data_cache_strategy() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let cold_data = TestData::new(300, "冷数据", 3000.0);
        let key = "cold:system:config:300";

        // 冷数据使用长TTL (4小时)
        let cold_ttl = Duration::from_secs(4 * 60 * 60);
        assert!(service.set(key, &cold_data, Some(cold_ttl)).await.is_ok());

        // 验证TTL设置正确
        let ttl = service.ttl(key).await.unwrap();
        assert!(ttl > 14390 && ttl <= 14400); // 允许一些时间误差

        // 清理
        let _ = service.delete(key).await;
    }
}

/// 缓存键命名空间测试
mod cache_namespace_tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_key_namespacing() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let test_data = TestData::new(400, "命名空间测试", 4000.0);

        // 测试不同命名空间的键
        let user_key = "user:400:profile";
        let room_key = "room:400:info";
        let message_key = "message:400:content";

        // 设置不同命名空间的数据
        assert!(service.set(user_key, &test_data, Some(Duration::from_secs(60))).await.is_ok());
        assert!(service.set(room_key, &test_data, Some(Duration::from_secs(60))).await.is_ok());
        assert!(service.set(message_key, &test_data, Some(Duration::from_secs(60))).await.is_ok());

        // 验证所有键都存在
        assert!(service.exists(user_key).await.unwrap());
        assert!(service.exists(room_key).await.unwrap());
        assert!(service.exists(message_key).await.unwrap());

        // 清理
        let _ = service.delete(user_key).await;
        let _ = service.delete(room_key).await;
        let _ = service.delete(message_key).await;
    }
}

/// 缓存失效和更新测试
mod cache_invalidation_tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_expiration_behavior() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let test_data = TestData::new(500, "过期测试", 5000.0);
        let key = "test:expiration:500";

        // 设置1秒TTL的缓存
        assert!(service.set(key, &test_data, Some(Duration::from_secs(1))).await.is_ok());

        // 立即检查存在
        assert!(service.exists(key).await.unwrap());

        // 等待过期
        sleep(Duration::from_millis(1100)).await;

        // 验证已过期
        assert!(!service.exists(key).await.unwrap());
        let retrieved: Option<TestData> = service.get(key).await.unwrap();
        assert!(retrieved.is_none());
    }

    #[tokio::test]
    async fn test_cache_update_behavior() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let original_data = TestData::new(501, "原始数据", 5010.0);
        let updated_data = TestData::new(501, "更新数据", 5020.0);
        let key = "test:update:501";

        // 设置原始数据
        assert!(service.set(key, &original_data, Some(Duration::from_secs(60))).await.is_ok());

        // 验证原始数据
        let retrieved: Option<TestData> = service.get(key).await.unwrap();
        assert_eq!(retrieved.unwrap().name, "原始数据");

        // 更新数据
        assert!(service.set(key, &updated_data, Some(Duration::from_secs(60))).await.is_ok());

        // 验证更新后的数据
        let retrieved: Option<TestData> = service.get(key).await.unwrap();
        assert_eq!(retrieved.unwrap().name, "更新数据");

        // 清理
        let _ = service.delete(key).await;
    }

    #[tokio::test]
    async fn test_cache_refresh_ttl() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let test_data = TestData::new(502, "TTL刷新测试", 5020.0);
        let key = "test:refresh:502";

        // 设置短TTL
        assert!(service.set(key, &test_data, Some(Duration::from_secs(3))).await.is_ok());

        // 等待1秒
        sleep(Duration::from_secs(1)).await;

        // 刷新TTL（重新设置相同数据但更长TTL）
        assert!(service.set(key, &test_data, Some(Duration::from_secs(60))).await.is_ok());

        // 验证TTL已更新
        let ttl = service.ttl(key).await.unwrap();
        assert!(ttl > 55 && ttl <= 60); // 应该接近60秒

        // 清理
        let _ = service.delete(key).await;
    }
}

/// 缓存错误处理测试
mod cache_error_handling_tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_nonexistent_key_operations() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let nonexistent_key = "test:nonexistent:999";

        // 获取不存在的键
        let retrieved: Option<TestData> = service.get(nonexistent_key).await.unwrap();
        assert!(retrieved.is_none());

        // 检查不存在的键
        assert!(!service.exists(nonexistent_key).await.unwrap());

        // 删除不存在的键（应该返回false）
        assert!(!service.delete(nonexistent_key).await.unwrap());

        // 获取不存在键的TTL（应该返回-2）
        let ttl = service.ttl(nonexistent_key).await.unwrap();
        assert_eq!(ttl, -2); // Redis标准：-2表示键不存在
    }

    #[tokio::test]
    async fn test_cache_invalid_data_handling() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        // 测试空字符串键（应该被处理）
        let empty_key = "";
        let test_data = TestData::new(600, "空键测试", 6000.0);

        // 尝试设置空键（可能会失败或被处理）
        let result = service.set(empty_key, &test_data, Some(Duration::from_secs(60))).await;
        // 不强制断言结果，因为不同实现可能有不同行为
        info!("空键设置结果: {:?}", result);
    }
}

/// 缓存并发安全测试
mod cache_concurrency_tests {
    use super::*;
    use std::sync::atomic::{ AtomicU32, Ordering };
    use tokio::task::JoinSet;

    #[tokio::test]
    async fn test_cache_concurrent_operations() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let service = Arc::new(service);
        let success_count = Arc::new(AtomicU32::new(0));
        let mut join_set = JoinSet::new();

        // 启动多个并发任务
        for i in 0..10 {
            let service_clone = Arc::clone(&service);
            let success_count_clone = Arc::clone(&success_count);

            join_set.spawn(async move {
                let test_data = TestData::new(
                    700 + i,
                    &format!("并发测试{i}"),
                    7000.0 + (i as f64)
                );
                let key = format!("test:concurrent:{i}");

                // 设置数据
                if service_clone.set(&key, &test_data, Some(Duration::from_secs(60))).await.is_ok() {
                    // 立即读取验证
                    if let Ok(Some(retrieved)) = service_clone.get::<TestData>(&key).await {
                        if retrieved.id == test_data.id {
                            success_count_clone.fetch_add(1, Ordering::SeqCst);
                        }
                    }

                    // 清理
                    let _ = service_clone.delete(&key).await;
                }
            });
        }

        // 等待所有任务完成
        while let Some(_) = join_set.join_next().await {}

        // 验证所有操作都成功
        let final_count = success_count.load(Ordering::SeqCst);
        assert_eq!(final_count, 10, "并发操作成功数量不符合预期");
    }

    #[tokio::test]
    async fn test_cache_race_condition_handling() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let service = Arc::new(service);
        let key = "test:race:condition";
        let mut join_set = JoinSet::new();

        // 多个任务同时更新同一个键
        for i in 0..5 {
            let service_clone = Arc::clone(&service);

            join_set.spawn(async move {
                let test_data = TestData::new(
                    800 + i,
                    &format!("竞态测试{i}"),
                    8000.0 + (i as f64)
                );
                service_clone.set(key, &test_data, Some(Duration::from_secs(60))).await
            });
        }

        // 等待所有任务完成
        let mut success_count = 0;
        while let Some(result) = join_set.join_next().await {
            if result.is_ok() && result.unwrap().is_ok() {
                success_count += 1;
            }
        }

        // 验证最终状态
        assert!(service.exists(key).await.unwrap());
        let final_data: Option<TestData> = service.get(key).await.unwrap();
        assert!(final_data.is_some());

        // 清理
        let _ = service.delete(key).await;

        info!("竞态条件测试完成，成功操作数: {}", success_count);
    }
}

/// 缓存性能基准测试
mod cache_performance_tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_bulk_operations_performance() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        let operation_count = 1000;
        let test_data = TestData::new(900, "性能测试", 9000.0);

        // 测试批量设置性能
        let start_time = std::time::Instant::now();
        for i in 0..operation_count {
            let key = format!("test:perf:bulk:{i}");
            let mut data = test_data.clone();
            data.id = 900 + i;

            assert!(service.set(&key, &data, Some(Duration::from_secs(300))).await.is_ok());
        }
        let set_duration = start_time.elapsed();

        // 测试批量获取性能
        let start_time = std::time::Instant::now();
        for i in 0..operation_count {
            let key = format!("test:perf:bulk:{i}");
            let _: Option<TestData> = service.get(&key).await.unwrap();
        }
        let get_duration = start_time.elapsed();

        // 测试批量删除性能
        let start_time = std::time::Instant::now();
        for i in 0..operation_count {
            let key = format!("test:perf:bulk:{i}");
            let _ = service.delete(&key).await;
        }
        let delete_duration = start_time.elapsed();

        // 性能报告
        info!("批量操作性能测试结果:");
        info!(
            "  设置{}个键耗时: {:?} (平均: {:?}/op)",
            operation_count,
            set_duration,
            set_duration / operation_count
        );
        info!(
            "  获取{}个键耗时: {:?} (平均: {:?}/op)",
            operation_count,
            get_duration,
            get_duration / operation_count
        );
        info!(
            "  删除{}个键耗时: {:?} (平均: {:?}/op)",
            operation_count,
            delete_duration,
            delete_duration / operation_count
        );

        // 性能断言（根据实际环境调整）
        assert!(set_duration.as_millis() < 10000, "批量设置性能不达标");
        assert!(get_duration.as_millis() < 5000, "批量获取性能不达标");
        assert!(delete_duration.as_millis() < 5000, "批量删除性能不达标");
    }

    #[tokio::test]
    async fn test_cache_large_data_handling() {
        let service = match create_test_cache_service().await {
            Ok(s) => s,
            Err(_) => {
                println!("跳过Redis测试 - 服务器不可用");
                return;
            }
        };

        // 创建大数据对象（1MB）
        let large_data = vec![0u8; 1024 * 1024];
        let large_test_data = TestData::new(1000, "大数据测试", 10000.0);

        #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
        struct LargeTestData {
            base: TestData,
            large_field: Vec<u8>,
        }

        let large_object = LargeTestData {
            base: large_test_data,
            large_field: large_data,
        };

        let key = "test:large:data";

        // 测试大数据设置
        let start_time = std::time::Instant::now();
        assert!(service.set(key, &large_object, Some(Duration::from_secs(60))).await.is_ok());
        let set_duration = start_time.elapsed();

        // 测试大数据获取
        let start_time = std::time::Instant::now();
        let retrieved: Option<LargeTestData> = service.get(key).await.unwrap();
        let get_duration = start_time.elapsed();

        assert!(retrieved.is_some());
        let retrieved_data = retrieved.unwrap();
        assert_eq!(retrieved_data.base.id, 1000);
        assert_eq!(retrieved_data.large_field.len(), 1024 * 1024);

        info!("大数据处理性能:");
        info!("  设置1MB数据耗时: {:?}", set_duration);
        info!("  获取1MB数据耗时: {:?}", get_duration);

        // 清理
        let _ = service.delete(key).await;

        // 性能断言（放宽测试环境的要求）
        assert!(set_duration.as_millis() < 5000, "大数据设置性能不达标: {:?}", set_duration);
        assert!(get_duration.as_millis() < 2000, "大数据获取性能不达标: {:?}", get_duration);
    }
}
